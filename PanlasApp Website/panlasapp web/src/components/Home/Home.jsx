/* eslint-disable no-unused-vars */
import { useState, useEffect, useMemo } from "react";
import Header from "../Header/Header";
import Sidebar from "../Sidebar/Sidebar";
import { FaHeart, FaRegHeart, FaTimes, FaChevronDown, FaChevronRight, FaStar, FaFire, FaClock } from "react-icons/fa";
import "../../../src/App.css";
import axios from "axios";
import { useFavorites } from "../../components/Favorites/FavoritesContext";
import userAPI from "../../services/userAPI";
import RecommendedMeals from "../RecommendedMeals/RecommendedMeals";
import analyticsService from "../../services/analyticsService";
import FloatingChatButton from "../FloatingChatButton/FloatingChatButton";
import Pagination from "../Pagination/Pagination";


function getDietaryBadges(meal) {
  const BADGE_MAP = [
    { key: 'Vegan', label: 'Vegan', color: '#4CAF50', icon: '🌱' },
    { key: 'Vegetarian', label: 'Vegetarian', color: '#8BC34A', icon: '🥬' },
    { key: 'Flexitarian', label: 'Flexitarian', color: '#A3C9A8', icon: '🤝' },
    { key: 'Dairy-Free', label: 'Dairy-Free', color: '#00BCD4', icon: '🥛🚫' },
    { key: 'Egg-Free', label: 'Egg-Free', color: '#FFEB3B', icon: '🥚🚫' },
    { key: 'Gluten-Free', label: 'Gluten-Free', color: '#FF9800', icon: '🌾' },
    { key: 'Soy-Free', label: 'Soy-Free', color: '#9E9E9E', icon: '🌱🚫' },
    { key: 'Nut-Free', label: 'Nut-Free', color: '#795548', icon: '🥜🚫' },
    { key: 'Low-Carb', label: 'Low-Carb', color: '#9C27B0', icon: '🥩' },
    { key: 'Low-Sugar', label: 'Low-Sugar', color: '#607D8B', icon: '🍬⬇️' },
    { key: 'Sugar-Free', label: 'Sugar-Free', color: '#607D8B', icon: '🍬🚫' },
    { key: 'Low-Fat', label: 'Low-Fat', color: '#03A9F4', icon: '🥗' },
    { key: 'Low-Sodium', label: 'Low-Sodium', color: '#B0BEC5', icon: '🧂⬇️' },
    { key: 'Organic', label: 'Organic', color: '#388E3C', icon: '🍃' },
    { key: 'Halal', label: 'Halal', color: '#2196F3', icon: '☪️' },
    { key: 'High-Protein', label: 'High-Protein', color: '#E91E63', icon: '💪' },
    { key: 'Pescatarian', label: 'Pescatarian', color: '#00B8D4', icon: '🐟' },
    { key: 'Keto', label: 'Keto', color: '#FFB300', icon: '🥓' },
    { key: 'Plant-Based', label: 'Plant-Based', color: '#43A047', icon: '🌿' },
    { key: 'Kosher', label: 'Kosher', color: '#3F51B5', icon: '✡️' },
    { key: 'Climatarian', label: 'Climatarian', color: '#689F38', icon: '🌎' },
    { key: 'Raw Food', label: 'Raw Food', color: '#AED581', icon: '🥗' },
    { key: 'Mediterranean', label: 'Mediterranean', color: '#00ACC1', icon: '🌊' },
    { key: 'Paleo', label: 'Paleo', color: '#A1887F', icon: '🍖' },
    { key: 'Kangatarian', label: 'Kangatarian', color: '#D84315', icon: '🦘' },
    { key: 'Pollotarian', label: 'Pollotarian', color: '#FBC02D', icon: '🍗' },
  ];

  const badges = [];
  const dietType = meal.dietType || meal.dietaryAttributes || {};
  const dietaryTags = (meal.dietaryTags || []).map(tag => tag.toLowerCase());

  for (const badge of BADGE_MAP) {
    const boolKey = 'is' + badge.key.replace(/[^a-zA-Z]/g, '');
    if (dietType[boolKey] === true) {
      badges.push(badge);
    } else if (dietaryTags.includes(badge.key.toLowerCase())) {
      badges.push(badge);
    }
  }
  return badges;
}
  

const Home = () => {
  // History context
  const RECENTLY_VIEWED_KEY = "recentlyViewedMeals";
  const MAX_RECENT = 10; // Show up to 10 recent meals

  function addRecentlyViewedMeal(meal) {
    let recent = JSON.parse(localStorage.getItem(RECENTLY_VIEWED_KEY)) || [];
    recent = recent.filter((m) => (m.id || m._id) !== (meal.id || meal._id));
    recent.unshift(meal);
    if (recent.length > MAX_RECENT) recent = recent.slice(0, MAX_RECENT);
    localStorage.setItem(RECENTLY_VIEWED_KEY, JSON.stringify(recent));
  }

  const [sidebarActive, setSidebarActive] = useState(false);
  const [activeFilter, setActiveFilter] = useState("All");
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [priceRange, setPriceRange] = useState("All");
  const [showRangeDropdown, setShowRangeDropdown] = useState(false);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [displayedCategories, setDisplayedCategories] = useState([]);
  const [hiddenCategories, setHiddenCategories] = useState([]);
  const [notification, setNotification] = useState({show: false,message: "",});
  const [filipinoMealData, setFilipinoMealData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userDishes, setUserDishes] = useState([]);
  const [userPreferences, setUserPreferences] = useState({});
  const [familyMembers, setFamilyMembers] = useState([]);

  // Search state
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [searchError, setSearchError] = useState(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;

  // Favorites context
  const { favorites, addFavorite, removeFavorite, isFavorite } = useFavorites();

  // Load user dietary preferences
  const loadUserPreferences = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token && token !== "undefined") {
        const response = await userAPI.getDietaryPreferences();
        if (response.success && response.dietaryPreferences) {
          setUserPreferences(response.dietaryPreferences);
        } else {
          setUserPreferences({});
        }
      }
    } catch (error) {
      setUserPreferences({});
    }
  };

  // Load family members
  const loadFamilyMembers = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token && token !== "undefined") {
        const response = await axios.get(
          "http://localhost:5000/api/users/family-members",
          { headers: { Authorization: `Bearer ${token}` } }
        );
        setFamilyMembers(response.data.familyMembers || []);
      }
    } catch (error) {
      console.error('Error loading family members:', error);
      setFamilyMembers([]);
    }
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth) => {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    if (isNaN(birthDate.getTime())) return null;

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Get age-based color for family member cards
  const getAgeBasedColor = (dateOfBirth) => {
    const age = calculateAge(dateOfBirth);

    if (age === null) {
      return '#8E8E93'; // Default color for unknown age (iOS gray)
    }

    if (age < 13) {
      return '#FF3B30'; // Vibrant red for children (0-12)
    } else if (age < 20) {
      return '#30D158'; // Vibrant green for teenagers (13-19)
    } else if (age < 35) {
      return '#007AFF'; // Vibrant blue for young adults (20-34)
    } else if (age < 55) {
      return '#FF9500'; // Vibrant orange for adults (35-54)
    } else {
      return '#AF52DE'; // Vibrant purple for seniors (55+)
    }
  };

  // Fetch meal data from API (send token if available)
  useEffect(() => {
    const fetchMealData = async () => {
      try {
        setLoading(true);
         const token = localStorage.getItem('token');
        const headers = token && token !== "undefined"
          ? { Authorization: `Bearer ${token}` }
          : {};
        const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
        const data = await axios.get(`${API_BASE_URL}/meals/filipino`);

        // Log sample meal data to understand structure
        if (data.data && data.data.length > 0) {
          console.log('📊 Sample meal data structure:', {
            name: data.data[0].name,
            dietType: data.data[0].dietType,
            dietaryAttributes: data.data[0].dietaryAttributes,
            dietaryTags: data.data[0].dietaryTags,
            ingredients: data.data[0].ingredients?.slice(0, 3),
            allergens: data.data[0].allergens
          });
        }

        setUserDishes(data.data);
        setFilipinoMealData(data.data);
        await loadUserPreferences();
        await loadFamilyMembers();
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    };
    fetchMealData();
  }, []);

  // Search API effect
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setSearchResults([]);
      setSearchLoading(false);
      setSearchError(null);
      return;
    }
    setSearchLoading(true);
    setSearchError(null);
    const delayDebounce = setTimeout(() => {
      const token = localStorage.getItem('token');
      const headers = token && token !== "undefined"
        ? { Authorization: `Bearer ${token}` }
        : {};
      axios
        .get("http://localhost:5000/api/meals/filipino", {
          params: { search: searchTerm },
          headers
        })
        .then((res) => {
          setSearchResults(res.data);
          setSearchLoading(false);
          analyticsService.trackSearch(searchTerm, {
            resultsCount: res.data.length,
            hasResults: res.data.length > 0
          });
        })
        .catch(() => {
          setSearchError("Error fetching search results");
          setSearchLoading(false);
        });
    }, 400);
    return () => clearTimeout(delayDebounce);
  }, [searchTerm]);

  // Add price range to each meal - with safety check
  const mealsWithPriceRange = useMemo(() => {
    const getPriceRange = (calories) => {
      if (calories < 300) return "Low";
      if (calories < 450) return "Mid";
      return "High";
    };
    return filipinoMealData
      ? filipinoMealData.map((meal) => ({
          ...meal,
          priceRange: getPriceRange(meal.calories),
        }))
      : [];
  }, [filipinoMealData]);

  const getPesoSigns = (priceRange) => {
    switch (priceRange) {
      case "Low": return "Low Range (₱50-150)";
      case "Mid": return "Mid Range (₱150-300)";
      case "High": return "High Range (₱300+)";
      default: return "₱";
    }
  };

const allDishes = mealsWithPriceRange;
const recommended = mealsWithPriceRange.slice(
  Math.min(40, mealsWithPriceRange.length),
  mealsWithPriceRange.length
);

  const categories = useMemo(() => {
    const allCategories = mealsWithPriceRange.flatMap((dish) =>
      Array.isArray(dish.category) ? dish.category : [dish.category]
    );
    return [
      "All",
      ...Array.from(
        new Set(
          allCategories.map(
            (c) => c && c.charAt(0).toUpperCase() + c.slice(1)
          )
        )
      ),
    ];
  }, [mealsWithPriceRange]);

  useEffect(() => {
    const MAX_VISIBLE_CATEGORIES = 5;
    if (categories.length <= MAX_VISIBLE_CATEGORIES) {
      setDisplayedCategories(categories);
      setHiddenCategories([]);
    } else {
      setDisplayedCategories(categories.slice(0, MAX_VISIBLE_CATEGORIES));
      setHiddenCategories(categories.slice(MAX_VISIBLE_CATEGORIES));
    }
  }, [categories]);

  const toggleSidebar = () => {
    setSidebarActive(!sidebarActive);
  };

  const toggleFavorite = (e, dish) => {
    e.stopPropagation();
    const dishId = dish.id || dish._id;
    if (isFavorite(dishId)) {
      removeFavorite(dishId);
      showNotification("Removed from favorites");
    } else {
      addFavorite(dish);
      showNotification("Added to favorites");
    }
  };

  const showNotification = (message) => {
    setNotification({ show: true, message });
    setTimeout(() => {
      setNotification({ show: false, message: "" });
    }, 3000);
  };

  const openMealDetails = (dish) => {
    setSelectedMeal(dish);
    setShowModal(true);

    analyticsService.trackMealView(dish.id || dish._id, dish.name);

    addRecentlyViewedMeal(dish);

    const token = localStorage.getItem("token");
    if (!token || token === "undefined") {
      console.warn("No token found. User must be logged in to save recently viewed meals.");
      return;
    }

    axios.post(
      "http://localhost:5000/api/users/recently-viewed-meals",
      { meal: dish },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json"
        }
      }
    ).catch((err) => {
      console.error("Failed to add recently viewed meal:", err);
    });
  };

  const closeMealDetails = () => {
    setShowModal(false);
  };

  const toggleRangeDropdown = () => {
    setShowRangeDropdown(!showRangeDropdown);
    if (showCategoryDropdown) setShowCategoryDropdown(false);
  };

  const toggleCategoryDropdown = () => {
    setShowCategoryDropdown(!showCategoryDropdown);
    if (showRangeDropdown) setShowRangeDropdown(false);
  };

  const selectPriceRange = (range) => {
    setPriceRange(range);
    setShowRangeDropdown(false);

    analyticsService.trackEvent('filter_apply', {
      filterType: 'priceRange',
      filterValue: range
    });
  };

  const selectCategory = (category) => {
    setActiveFilter(category);
    setShowCategoryDropdown(false);

    analyticsService.trackEvent('filter_apply', {
      filterType: 'category',
      filterValue: category
    });
  };

  const filterMealsByDietaryPreferences = (meals) => {
    let filtered = meals;

    // Combine user and family dietary preferences
    const allRestrictions = [...(userPreferences?.restrictions || [])];
    const allAllergies = [...(userPreferences?.allergies || [])];

    // Add family member preferences if they exist
    if (familyMembers && familyMembers.length > 0) {
      familyMembers.forEach(member => {
        if (member.dietaryPreferences) {
          allRestrictions.push(...(member.dietaryPreferences.restrictions || []));
          allAllergies.push(...(member.dietaryPreferences.allergies || []));
        }
      });
    }

    // Remove duplicates
    const uniqueRestrictions = [...new Set(allRestrictions)];
    const uniqueAllergies = [...new Set(allAllergies)];

    // Only apply filtering if there are actual preferences to filter by
    if (uniqueRestrictions.length === 0 && uniqueAllergies.length === 0) {
      return filtered; // No dietary preferences to filter by, return all meals
    }

    if (uniqueRestrictions.length > 0 || uniqueAllergies.length > 0) {
      filtered = filtered.filter(meal => {
        const dietType = meal.dietType || meal.dietaryAttributes || {};
        const dietaryTags = meal.dietaryTags || [];
        const ingredients = meal.ingredients || [];
        const mealName = meal.name || '';

        if (uniqueRestrictions.length > 0) {
          const meetsRestrictions = uniqueRestrictions.every(restriction => {
            let meets = false;
            switch(restriction) {
              case 'Vegetarian':
                meets = dietType.isVegetarian === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('vegetarian')) ||
                        !ingredients.some(ing =>
                          ['pork', 'beef', 'chicken', 'fish', 'meat', 'seafood', 'shrimp', 'crab'].some(meat =>
                            ing.toLowerCase().includes(meat)
                          )
                        );
                break;
              case 'Vegan':
                meets = dietType.isVegan === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('vegan')) ||
                        (!ingredients.some(ing =>
                          ['pork', 'beef', 'chicken', 'fish', 'meat', 'seafood', 'dairy', 'milk', 'cheese', 'egg', 'butter'].some(animal =>
                            ing.toLowerCase().includes(animal)
                          )
                        ));
                break;
              case 'Gluten-Free':
                meets = dietType.isGlutenFree === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('gluten-free')) ||
                        !ingredients.some(ing =>
                          ['wheat', 'flour', 'bread', 'pasta', 'noodles', 'soy sauce'].some(gluten =>
                            ing.toLowerCase().includes(gluten)
                          )
                        );
                break;
              case 'Dairy-Free':
                meets = dietType.isDairyFree === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('dairy-free')) ||
                        !ingredients.some(ing =>
                          ['milk', 'cheese', 'butter', 'cream', 'yogurt', 'dairy'].some(dairy =>
                            ing.toLowerCase().includes(dairy)
                          )
                        );
                break;
              case 'Nut-Free':
                meets = dietType.isNutFree === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('nut-free')) ||
                        !ingredients.some(ing =>
                          ['peanut', 'almond', 'walnut', 'cashew', 'pecan', 'hazelnut', 'pistachio', 'nuts'].some(nut =>
                            ing.toLowerCase().includes(nut)
                          )
                        );
                break;
              case 'Low-Carb':
                meets = dietType.isLowCarb === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('low-carb')) ||
                        (meal.carbs && meal.carbs < 20);
                break;
              case 'Keto':
                meets = dietType.isKeto === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('keto')) ||
                        (meal.carbs && meal.carbs < 10 && meal.fat && meal.fat > 15);
                break;
              case 'Pescatarian':
                meets = dietType.isPescatarian === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('pescatarian')) ||
                        !ingredients.some(ing =>
                          ['pork', 'beef', 'chicken', 'meat', 'lamb', 'turkey'].some(meat =>
                            ing.toLowerCase().includes(meat)
                          )
                        );
                break;
              case 'Halal':
                meets = dietType.isHalal === true ||
                        dietaryTags.some(tag => tag.toLowerCase().includes('halal')) ||
                        (!ingredients.some(ing =>
                          ['pork', 'ham', 'bacon', 'alcohol', 'wine', 'beer', 'gelatin'].some(nonHalal =>
                            ing.toLowerCase().includes(nonHalal)
                          )
                        ) && !mealName.toLowerCase().includes('pork'));
                break;
              default:
                meets = true;
            }
            return meets;
          });
          if (!meetsRestrictions) return false;
        }

        if (uniqueAllergies.length > 0) {
          const ingredients = meal.ingredients || [];
          const hasAllergen = uniqueAllergies.some(allergy =>
            ingredients.some(ingredient =>
              ingredient.toLowerCase().includes(allergy.toLowerCase())
            )
          );
          if (hasAllergen) {
            return false;
          }
        }

        if (userPreferences.dislikedIngredients && userPreferences.dislikedIngredients.length > 0) {
          const ingredients = meal.ingredients || [];
          const hasDislikedIngredient = userPreferences.dislikedIngredients.some(disliked =>
            ingredients.some(ingredient =>
              ingredient.toLowerCase().includes(disliked.toLowerCase())
            )
          );
          if (hasDislikedIngredient) {
            return false;
          }
        }

        return true;
      });
    }
    return filtered;
  };

  const filterDishes = (dishes) => {
    let filtered = dishes;
    filtered = filterMealsByDietaryPreferences(filtered);

    if (activeFilter !== "All") {
      filtered = filtered.filter((dish) =>
        Array.isArray(dish.category)
          ? dish.category
              .map((c) => c.toLowerCase())
              .includes(activeFilter.toLowerCase())
          : (dish.category || "").toLowerCase() === activeFilter.toLowerCase()
      );
    }

    if (priceRange !== "All") {
      filtered = filtered.filter((dish) => dish.priceRange === priceRange);
    }

    return filtered;
  };



  const filteredDishes = filterDishes(allDishes);

  const dishesToShow = searchTerm.trim()
    ? searchResults.map((meal) => ({
        ...meal,
        priceRange:
          meal.calories < 300
            ? "Low"
            : meal.calories < 450
            ? "Mid"
            : "High",
      }))
    : filteredDishes;

  const totalPages = Math.ceil(dishesToShow.length / itemsPerPage);
  const paginatedDishes = dishesToShow.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  const handlePageChange = (page) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  if (loading) {
    return (
      <div className="app-container">
        <Header
          toggleSidebar={toggleSidebar}
          openMealDetails={openMealDetails}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="container">
              <h1 className="loading-title">Loading Filipino meals...</h1>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="app-container">
        <Header
          toggleSidebar={toggleSidebar}
          openMealDetails={openMealDetails}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="container">
              <h1 className="error-title">Error</h1>
              <p className="error-message">{error}</p>
              <button className="view-meal-btn" onClick={() => window.location.reload()}>
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!filipinoMealData || filipinoMealData.length === 0) {
    return (
      <div className="app-container">
        <Header
          toggleSidebar={toggleSidebar}
          openMealDetails={openMealDetails}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
        />
        <Sidebar isActive={sidebarActive} />
        <div className="content-area">
          <div className="main-content">
            <div className="container">
              <h1 className="no-results">No Filipino meals found</h1>
            </div>
          </div>
        </div>
      </div>
    );
  }

return (
  <div className="app-container">
    <Header
      toggleSidebar={toggleSidebar}
      openMealDetails={openMealDetails}
      searchTerm={searchTerm}
      setSearchTerm={setSearchTerm}
    />
    <Sidebar isActive={sidebarActive} />
    <div className="content-area">
      <div className="main-content">
        <h1 className="page-title">HOME</h1>

        {/* Family Dietary Preferences Display */}
        {familyMembers.length > 0 && (
          <div className="family-preferences-section">
            <div className="family-preferences-header">
              <h2>Family Dietary Preferences</h2>
              <a href="/family" className="manage-family-link">Manage Family</a>
            </div>
            <div className="family-members-grid">
              {familyMembers.map((member, index) => (
                <div
                  key={member._id || index}
                  className="family-member-card"
                  style={{ borderLeftColor: getAgeBasedColor(member.dateOfBirth), borderLeftWidth: '4px' }}
                >
                  <div className="family-member-info">
                    <div className="family-member-header">
                      <h3 className="family-member-name">{member.name}</h3>
                      {member.dateOfBirth && calculateAge(member.dateOfBirth) && (
                        <span className="family-member-age">
                          Age: {calculateAge(member.dateOfBirth)}
                        </span>
                      )}
                    </div>
                    <p className="family-member-preferences">
                      {member.dietaryPreferences?.restrictions?.length > 0 || member.dietaryPreferences?.allergies?.length > 0
                        ? [
                            ...(member.dietaryPreferences.restrictions || []),
                            ...(member.dietaryPreferences.allergies || [])
                          ].join(', ')
                        : 'No dietary preferences set'
                      }
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Filter Controls */}
        <div className="filter-controls">
          {/* Category Tabs and Dropdown */}
          <div className="filter-section">
            <div className="filter-tabs">
              {displayedCategories.map((filter) => (
                <button
                  key={filter}
                  className={`filter-tab ${activeFilter === filter ? "active" : ""}`}
                  onClick={() => {
                    setActiveFilter(filter);
                    setCurrentPage(1);
                  }}
                >
                  {filter}
                </button>
              ))}
              {hiddenCategories.length > 0 && (
                <div className="category-dropdown-container">
                  <button
                    className={`category-dropdown-button ${hiddenCategories.includes(activeFilter) ? "active" : ""}`}
                    onClick={toggleCategoryDropdown}
                  >
                    {hiddenCategories.includes(activeFilter) ? activeFilter : "More"} <FaChevronDown />
                  </button>
                  {showCategoryDropdown && (
                    <div className="dropdown-menu">
                      {hiddenCategories.map((category) => (
                        <button
                          key={category}
                          className={activeFilter === category ? "active" : ""}
                          onClick={() => {
                            selectCategory(category);
                            setCurrentPage(1);
                          }}
                        >
                          {category}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          {/* Price Range Dropdown */}
          <div className="range-dropdown-container">
            <button className="range-dropdown-button" onClick={toggleRangeDropdown}>
              Range: {priceRange === "All" ? "All" : getPesoSigns(priceRange)} <FaChevronDown />
            </button>
            {showRangeDropdown && (
              <div className="dropdown-menu">
                <button onClick={() => { selectPriceRange("All"); setCurrentPage(1); }}>All Ranges</button>
                <button onClick={() => { selectPriceRange("Low"); setCurrentPage(1); }}>Low Range (₱50-150)</button>
                <button onClick={() => { selectPriceRange("Mid"); setCurrentPage(1); }}>Mid Range (₱150-300)</button>
                <button onClick={() => { selectPriceRange("High"); setCurrentPage(1); }}>High Range (₱300+)</button>
              </div>
            )}
          </div>
        </div>
        {/* Dietary Preferences Indicator */}
        {userPreferences && (userPreferences.restrictions?.length > 0 || userPreferences.allergies?.length > 0) && (
          <div className="dietary-info-section">
            <div className="dietary-info-header">
              <span>🍃 Active Dietary Filters:</span>
              <button
                className="edit-preferences-btn"
                onClick={() => window.location.href = '/dietary-preferences'}
              >
                Edit Preferences
              </button>
            </div>
            <div className="dietary-info-content">
              {userPreferences.restrictions?.length > 0 && (
                <div className="dietary-info-item">
                  <span className="dietary-label">Restrictions:</span>
                  <span className="dietary-values">{userPreferences.restrictions.join(', ')}</span>
                </div>
              )}
              {userPreferences.allergies?.length > 0 && (
                <div className="dietary-info-item">
                  <span className="dietary-label">Allergies:</span>
                  <span className="dietary-values">{userPreferences.allergies.join(', ')}</span>
                </div>
              )}
            </div>
          </div>
        )}
        {/* Notification */}
        {notification.show && (
          <div className="notification">{notification.message}</div>
        )}
        {/* Search loading/error */}
        {searchLoading && (
          <div className="search-loading">Loading search results...</div>
        )}
        {searchError && (
          <div className="search-error">{searchError}</div>
        )}
        {/* Meals For You section FIRST */}
        <h2 className="section-title">
          {searchTerm.trim() ? "Search Results" : "Meals For You"}
        </h2>
        <div className="food-grid">
          {paginatedDishes.length > 0 ? (
            paginatedDishes.map((dish, index) => (
              <div
                key={dish.id || dish._id}
                className="recommendation-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="recommendation-image">
                  {dish.image ? (
                    <img src={dish.image} alt={dish.name} />
                  ) : (
                    <div className="meal-placeholder">🍽️</div>
                  )}
                  <button
                    className={`favorite-btn ${isFavorite(dish.id || dish._id) ? 'favorited' : ''}`}
                    onClick={(e) => toggleFavorite(e, dish)}
                    title={isFavorite(dish.id || dish._id) ? "Remove from favorites" : "Add to favorites"}
                  >
                    {isFavorite(dish.id || dish._id) ? <FaHeart /> : <FaRegHeart />}
                  </button>
                  {dish.rating && (
                    <div className="recommendation-score">
                      <FaStar /> {dish.rating}
                    </div>
                  )}
                </div>
                <div className="recommendation-content">
                  <h3 className="meal-name">{dish.name}</h3>
                  <div className="meal-meta">
                    <div className="meal-stats">
                      <span className="calories">
                        <FaFire /> {dish.calories} cal
                      </span>
                      {dish.prepTime && (
                        <span className="prep-time">
                          <FaClock /> {dish.prepTime}m
                        </span>
                      )}
                      {dish.rating && (
                        <span className="rating">
                          <FaStar /> {dish.rating}
                        </span>
                      )}
                    </div>
                    <span className="meal-range-label">
                      {getPesoSigns(dish.priceRange)}
                    </span>
                  </div>
                  {/* Dietary badges here */}
                  {getDietaryBadges(dish).length > 0 && (
                    <div className="dietary-badges">
                      {getDietaryBadges(dish).map((badge, idx) => (
                        <span
                          key={idx}
                          className="dietary-badge"
                          style={{
                            backgroundColor: badge.color,
                            color: "#fff",
                            borderRadius: "18px",
                            padding: "2px 8px",
                            fontSize: "0.65em",
                            fontWeight: 600,
                            marginTop: "6px",
                            display: "inline-block",
                            marginRight: "6px"
                          }}
                        >
                          {badge.icon} {badge.label}
                        </span>
                      ))}
                    </div>
                  )}
                  <p className="meal-description">
                    {dish.description?.substring(0, 80)}
                    {dish.description?.length > 80 ? '...' : ''}
                  </p>
                  <button
                    className="view-meal-btn"
                    onClick={() => openMealDetails(dish)}
                  >
                    View Meal <FaChevronRight />
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="no-results">
              <p>
                {searchTerm.trim()
                  ? "No dishes found for your search."
                  : "No dishes found matching the selected filters."}
              </p>
            </div>
          )}
        </div>
        {/* Pagination Controls */}
{totalPages > 1 && (
  <Pagination
    currentPage={currentPage}
    totalPages={totalPages}
    onPageChange={handlePageChange}
    maxPageButtons={5} // Show 5 numbered buttons at a time
  />
)}
        {/* RecommendedMeals section SECOND */}
        {!searchTerm.trim() && (
          <>
            <RecommendedMeals onMealClick={openMealDetails} />
          </>
        )}
        {/* Meal Details Modal */}
        {showModal && selectedMeal && (
          <div className="modal-overlay">
            <div className="modal-content">
              <div className="modal-header">
                <h2>{selectedMeal.name}</h2>
                <button className="close-modal" onClick={closeMealDetails}>
                  <FaTimes />
                </button>
              </div>
              <div className="modal-body">
                <div className="meal-image">
                  <img src={selectedMeal.image} alt={selectedMeal.name} />
                </div>
                <div className="meal-details">
                  <p className="meal-description">
                    {selectedMeal.description}
                  </p>
                  <div className="meal-meta">
                    <span className="meal-rating">
                      {selectedMeal.rating} &#9733;
                    </span>
                    <span className="meal-category">
                      {selectedMeal.category}
                    </span>
                    <span className="meal-price">
                      Calories: {selectedMeal.calories} (
                      {selectedMeal.priceRange} Range)
                    </span>
                  </div>
                  {selectedMeal.ingredients && selectedMeal.ingredients.length > 0 && (
                    <div className="meal-ingredients">
                      <h3>Ingredients</h3>
                      <ul className="ingredients-list">
                        {selectedMeal.ingredients.map((ingredient, idx) => (
                          <li key={idx}>{ingredient}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  <div className="meal-nutrition">
                    <h3>Nutrition Information</h3>
                    <div className="nutrition-grid">
                      <div className="nutrition-item">
                        <span className="nutrition-label">Protein:</span>
                        <span className="nutrition-value">
                          {selectedMeal.protein}g
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Carbs:</span>
                        <span className="nutrition-value">
                          {selectedMeal.carbs}g
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Fat:</span>
                        <span className="nutrition-value">
                          {selectedMeal.fat}g
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Calories:</span>
                        <span className="nutrition-value">
                          {selectedMeal.calories}
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Calcium:</span>
                        <span className="nutrition-value">
                          {selectedMeal.calcium} mg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Phosphorus:</span>
                        <span className="nutrition-value">
                          {selectedMeal.phosphorus} mg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Iron:</span>
                        <span className="nutrition-value">
                          {selectedMeal.iron} mg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin A:</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminA} µg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin C:</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminC} mg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B1 (Thiamine):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB1} mg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B2 (Riboflavin):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB2} mg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B3 (Niacin):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB3} mg
                        </span>
                      </div>
                      <div className="nutrition-item">
                        <span className="nutrition-label">Prep Time:</span>
                        <span className="nutrition-value">
                          {selectedMeal.prepTime} mins
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="meal-steps">
                    <h3>Cooking Instructions</h3>
                    <ol>
                      {selectedMeal.instructions &&
                        selectedMeal.instructions.map((step, idx) => (
                          <li key={idx}>{step}</li>
                        ))}
                    </ol>
                  </div>
                  <div className="meal-tags">
                    <h3>Dietary Tags</h3>
                    <div className="tags-container">
                      {selectedMeal.dietaryTags &&
                        selectedMeal.dietaryTags.map((tag, idx) => (
                          <span key={idx} className="dietary-tag">
                            {tag}
                          </span>
                        ))}
                    </div>
                  </div>
                  <div className="meal-types">
                    <h3>Meal Types</h3>
                    <div className="tags-container">
                      {selectedMeal.mealType &&
                        selectedMeal.mealType.map((type, idx) => (
                          <span key={idx} className="meal-type-tag">
                            {type}
                          </span>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  className="favorite-modal-btn"
                  onClick={(e) => toggleFavorite(e, selectedMeal)}
                >
                  {isFavorite(selectedMeal.id || selectedMeal._id) ? (
                    <>
                      Remove from Favorites <FaHeart />
                    </>
                  ) : (
                    <>
                      Add to Favorites <FaRegHeart />
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
    {/* Floating Chat Button */}
    <FloatingChatButton />
  </div>
);
};

export default Home;
